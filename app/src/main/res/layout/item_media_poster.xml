<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/poster_width_6col"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_margin="@dimen/poster_margin_6col"
    android:focusable="true"
    android:focusableInTouchMode="false"
    android:clickable="true">

    <!-- 现代化海报容器 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/poster_height_6col"
        android:layout_margin="2dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/card_background"
        android:focusable="false"
        android:clickable="false">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 海报图片 -->
            <ImageView
                android:id="@+id/iv_poster"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="matrix"
                android:src="@drawable/ic_video"
                android:focusable="false"
                android:duplicateParentState="true"
                android:clickable="false"
                android:background="@drawable/poster_image_border"
                android:foreground="@drawable/poster_image_border" />

            <!-- 评分标签 -->
            <TextView
                android:id="@+id/tv_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="6dp"
                android:text="8.5"
                android:textSize="10sp"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:background="@drawable/rating_badge_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="3dp"
                android:visibility="gone" />

            <!-- 观看进度 -->
            <LinearLayout
                android:id="@+id/layout_progress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_margin="6dp"
                android:orientation="vertical"
                android:background="@drawable/progress_background"
                android:padding="6dp"
                android:visibility="gone">

                <ProgressBar
                    android:id="@+id/progress_bar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="3dp"
                    android:progressTint="@color/accent_color"
                    android:progressBackgroundTint="@color/white"
                    android:alpha="0.8" />

                <TextView
                    android:id="@+id/tv_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="30%"
                    android:textSize="9sp"
                    android:textColor="@color/white"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <!-- 标题信息区域（在海报下方） -->
    <LinearLayout
        android:id="@+id/layout_bottom_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="4dp"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:focusable="false"
        android:clickable="false">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="电影标题"
            android:textSize="12sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="start"
            android:focusable="false" />

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2023 • 120分钟"
            android:textSize="10sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="1dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="start"
            android:visibility="visible"
            android:focusable="false" />

    </LinearLayout>

</LinearLayout>
