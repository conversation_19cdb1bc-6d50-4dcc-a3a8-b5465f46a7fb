<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 外层白色发光效果 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="10dp" />
            <stroke
                android:width="4dp"
                android:color="#40FFFFFF" />
        </shape>
    </item>
    
    <!-- 主要白色边框 -->
    <item android:inset="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
            <stroke
                android:width="3dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
    
</layer-list>
