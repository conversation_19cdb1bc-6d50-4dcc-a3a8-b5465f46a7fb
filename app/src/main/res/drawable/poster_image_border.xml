<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 获得焦点时的状态 - 白色边框加分割线 -->
    <item android:state_focused="true">
        <layer-list>
            <!-- 外层白色发光效果 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <corners android:radius="8dp" />
                    <stroke
                        android:width="4dp"
                        android:color="#40FFFFFF" />
                </shape>
            </item>
            
            <!-- 主要白色边框 -->
            <item android:inset="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <corners android:radius="6dp" />
                    <stroke
                        android:width="3dp"
                        android:color="#FFFFFF" />
                </shape>
            </item>
            
            <!-- 内层深色分割线，与图片形成对比 -->
            <item android:inset="6dp">
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <corners android:radius="4dp" />
                    <stroke
                        android:width="2dp"
                        android:color="#80000000" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- 按下时的状态 -->
    <item android:state_pressed="true">
        <layer-list>
            <!-- 白色边框 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <corners android:radius="8dp" />
                    <stroke
                        android:width="2dp"
                        android:color="#FFFFFF" />
                </shape>
            </item>
            <!-- 分割线 -->
            <item android:inset="3dp">
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <corners android:radius="5dp" />
                    <stroke
                        android:width="1dp"
                        android:color="#60000000" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>